import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import { expect, test } from '@jest/globals';

const specPath = path.join(__dirname, '..', 'api', 'mcp-rules-config-prod.yaml');

test('OpenAPI backend URL rendered', () => {
  const raw = fs.readFileSync(specPath, 'utf8');
  const doc: any = yaml.load(raw);
  // Traverse paths to find x-google-backend addresses
  const addresses: string[] = [];
  Object.values<any>(doc.paths).forEach((p) => {
    Object.values<any>(p).forEach((method) => {
      if (method['x-google-backend']?.address) {
        addresses.push(method['x-google-backend'].address as string);
      }
    });
  });

  addresses.forEach((addr) => {
    expect(addr).toMatch(/^https:\/\//);
    expect(addr).not.toMatch(/\$\{.+}/); // no unresolved template tokens
  });
});
