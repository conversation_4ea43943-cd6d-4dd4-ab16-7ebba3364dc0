# MCP Rules Engine Production API Gateway Configuration
# OpenAPI 2.0 specification for Google Cloud API Gateway
# This configuration maps the production API Gateway to the Cloud Run service

swagger: "2.0"
info:
  title: MCP Rules API
  description: Production API Gateway for MCP Rules Engine - Legal Deadline Calculation Service
  version: "1.0"
  contact:
    name: AiLex Engineering Team
    email: <EMAIL>

# API Gateway host - this will be the custom domain
host: mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev

schemes: 
  - https

# Base path for all endpoints
basePath: /

paths:
  /health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the MCP Rules Engine service
      operationId: health_check
      security: []  # Override global security - make health check public
      x-google-backend:
        address: ${CLOUD_RUN_URL}/health
      produces:
        - application/json
      responses:
        '200':
          description: Service is healthy
          schema:
            type: object
            properties:
              status:
                type: string
                example: "healthy"
              timestamp:
                type: string
                format: date-time
        '503':
          description: Service unavailable
          schema:
            type: object
            properties:
              status:
                type: string
                example: "unhealthy"
              error:
                type: string

  /mcp/run:
    post:
      summary: Execute MCP Rules Engine operations
      description: Calculate legal deadlines based on jurisdiction, trigger code, and start date
      operationId: mcp_run
      x-google-backend:
        address: ${CLOUD_RUN_URL}/mcp/run
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: header
          name: x-api-key
          type: string
          required: true
          description: API key for authentication and tenant identification
        - in: body
          name: body
          required: true
          description: MCP operation request payload
          schema:
            type: object
            required:
              - toolName
              - params
            properties:
              toolName:
                type: string
                enum: ["calculate_deadlines"]
                description: The MCP tool to execute
              params:
                type: object
                required:
                  - jurisdiction
                  - triggerCode
                  - startDate
                properties:
                  jurisdiction:
                    type: string
                    description: Legal jurisdiction code (e.g., TX_STATE, CA_STATE)
                    example: "TX_STATE"
                  triggerCode:
                    type: string
                    description: Legal trigger event code
                    example: "SERVICE_OF_PROCESS"
                  startDate:
                    type: string
                    format: date
                    description: Start date for deadline calculations (YYYY-MM-DD)
                    example: "2025-08-01"
      responses:
        '200':
          description: Successful deadline calculation
          schema:
            type: object
            properties:
              deadlines:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                      description: Deadline name
                    date:
                      type: string
                      format: date
                      description: Calculated deadline date
                    description:
                      type: string
                      description: Deadline description
                    priority:
                      type: string
                      enum: ["high", "medium", "low"]
                      description: Deadline priority level
        '400':
          description: Bad request - invalid parameters
          schema:
            type: object
            properties:
              error:
                type: string
                description: Error message
              details:
                type: object
                description: Additional error details
        '401':
          description: Unauthorized - invalid or missing API key
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Invalid API key"
        '403':
          description: Forbidden - API key lacks required permissions
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Insufficient permissions"
        '429':
          description: Too many requests - rate limit exceeded
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Rate limit exceeded"
              retryAfter:
                type: integer
                description: Seconds to wait before retrying
        '500':
          description: Internal server error
          schema:
            type: object
            properties:
              error:
                type: string
                description: Error message
              requestId:
                type: string
                description: Request ID for debugging

# Security definitions for Google Cloud API Gateway
securityDefinitions:
  api_key:
    type: "apiKey"
    name: "key"
    in: "query"
  api_key_header:
    type: "apiKey"
    name: "x-api-key"
    in: "header"

# Apply security to all endpoints
security:
  - api_key: []
  - api_key_header: []

# Backend service configuration
x-google-backend:
  deadline: 30.0
