{"version": 4, "terraform_version": "1.5.7", "serial": 169, "lineage": "ca5901af-c120-8608-db7b-51fbfa241a41", "outputs": {"api_config_id": {"value": "mcp-rules-config-prod-v2", "type": "string"}, "api_gateway_id": {"value": "mcp-rules-gateway-prod", "type": "string"}, "api_gateway_url": {"value": "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "type": "string"}, "prod_gateway_host": {"value": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "type": "string"}, "ssl_certificate_id": {"value": "projects/texas-laws-personalinjury/global/sslCertificates/mcp-rules-gateway-ssl", "type": "string"}}, "resources": [{"mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"]", "instances": [{"schema_version": 0, "attributes": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "MCP Rules Engine API", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"], "create_before_destroy": true}]}, {"mode": "managed", "type": "google_api_gateway_api_config", "name": "mcp_rules_config", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"]", "instances": [{"schema_version": 0, "attributes": {"api": "mcp-rules-gateway", "api_config_id": "mcp-rules-config-prod-v2", "api_config_id_prefix": null, "display_name": "MCP Rules Engine Production Config v2", "effective_labels": {}, "gateway_config": [], "grpc_services": [], "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-v2", "labels": {}, "managed_service_configs": [], "name": "projects/122290401475/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-v2", "openapi_documents": [{"document": [{"contents": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "path": "mcp-rules-config-prod.yaml"}]}], "project": "texas-laws-personalinjury", "service_config_id": "mcp-rules-config-prod-v2-0y8l23hwjrdbx", "terraform_labels": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMCJ9"}]}, {"mode": "managed", "type": "google_api_gateway_gateway", "name": "prod", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"]", "instances": [{"schema_version": 0, "attributes": {"api_config": "projects/122290401475/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-final", "default_hostname": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "display_name": "MCP Rules Gateway Production", "effective_labels": {}, "gateway_id": "mcp-rules-gateway-prod", "id": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "labels": {}, "name": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "project": "texas-laws-personalinjury", "region": "us-central1", "terraform_labels": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMCJ9", "dependencies": ["google_api_gateway_api.mcp_rules_api", "google_api_gateway_api_config.mcp_rules_config", "google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"]}]}, {"mode": "managed", "type": "google_apikeys_key", "name": "mcp_api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "function_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "gateway_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY4HLxGEyk=", "id": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod/roles/run.invoker/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "texas-laws-personalinjury", "role": "roles/run.invoker", "service": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "google_cloudfunctions2_function", "name": "tenant_onboard", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_compute_managed_ssl_certificate", "name": "mcp_rules_ssl", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"certificate_id": 7925036411692338119, "creation_timestamp": "2025-06-22T23:33:12.615-07:00", "description": "", "expire_time": "", "id": "projects/texas-laws-personalinjury/global/sslCertificates/mcp-rules-gateway-ssl", "managed": [{"domains": ["rules.ailexlaw.com"]}], "name": "mcp-rules-gateway-ssl", "project": "texas-laws-personalinjury", "self_link": "https://www.googleapis.com/compute/v1/projects/texas-laws-personalinjury/global/sslCertificates/mcp-rules-gateway-ssl", "subject_alternative_names": [], "timeouts": null, "type": "MANAGED"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDB9fQ==", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_latency", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"alert_strategy": [{"auto_close": "1800s", "notification_channel_strategy": [], "notification_rate_limit": []}], "combiner": "OR", "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"alignment_period": "60s", "cross_series_reducer": "REDUCE_PERCENTILE_95", "group_by_fields": ["resource.label.service"], "per_series_aligner": "ALIGN_DELTA"}], "comparison": "COMPARISON_GT", "denominator_aggregations": [], "denominator_filter": "", "duration": "300s", "evaluation_missing_data": "", "filter": "resource.type=\"api\" AND metric.type=\"serviceruntime.googleapis.com/api/request_latencies\" AND resource.labels.service=\"mcp-rules-gateway\"", "forecast_options": [], "threshold_value": 700, "trigger": []}], "display_name": "P95 latency > 700ms", "name": "projects/texas-laws-personalinjury/alertPolicies/7115721945882692301/conditions/7115721945882689724"}], "creation_record": [{"mutate_time": "2025-06-23T07:04:15.411054456Z", "mutated_by": "<EMAIL>"}], "display_name": "MCP Rules Gateway - High Latency", "documentation": [], "enabled": true, "id": "projects/texas-laws-personalinjury/alertPolicies/7115721945882692301", "name": "projects/texas-laws-personalinjury/alertPolicies/7115721945882692301", "notification_channels": [], "project": "texas-laws-personalinjury", "severity": "", "timeouts": null, "user_labels": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_firestore_user", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_run_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "mcp_key_rotator_binding", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_service", "name": "apigateway", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInJlYWQiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjoxMjAwMDAwMDAwMDAwfX0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInJlYWQiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjoxMjAwMDAwMDAwMDAwfX0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInJlYWQiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjoxMjAwMDAwMDAwMDAwfX0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_rules_base", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_rules_base_staging", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "env_vars_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "env_vars_staging_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "mcp_secret_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_api_key_versions", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_initial_versions", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_rules_base_prod", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_rules_base_staging_value", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "rotator_sa_key_version", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_service_account_iam_member", "name": "allow_impersonation", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_service_account_key", "name": "mcp_key_rotator_key", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}], "check_results": null}