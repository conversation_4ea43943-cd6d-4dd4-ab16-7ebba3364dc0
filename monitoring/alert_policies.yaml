# MCP Rules Engine - Cloud Monitoring Alert Policies
# Google Cloud Monitoring API v3 format for alert policy definitions

# Alert Policy 1: API Gateway Error Rate > 2% in 5 minutes
api_gateway_error_rate:
  displayName: "MCP Rules Gateway - High Error Rate"
  documentation:
    content: |
      This alert fires when the API Gateway error rate exceeds 2% over a 5-minute period.
      
      ## Troubleshooting Steps:
      1. Check API Gateway logs for error patterns
      2. Verify Cloud Run backend health
      3. Check API key validity and quotas
      4. Review recent deployments or configuration changes
      
      ## Escalation:
      - If error rate > 5%: Page on-call engineer
      - If error rate > 10%: Consider emergency rollback
    mimeType: "text/markdown"
  
  combiner: OR
  
  conditions:
    - displayName: "Error rate > 2% for 5 minutes"
      conditionThreshold:
        filter: |
          metric.type="serviceruntime.googleapis.com/api/request_count"
          AND metric.label.response_code_class="5xx"
          AND resource.labels.service="mcp-rules-gateway"
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 0.02
        duration: "300s"
        
        aggregations:
          - alignmentPeriod: "60s"
            perSeriesAligner: ALIGN_RATE
            crossSeriesReducer: REDUCE_SUM
            groupByFields:
              - "resource.label.service"
              - "resource.label.method"
        
        trigger:
          count: 1
  
  alertStrategy:
    autoClose: "1800s"  # 30 minutes
    
  notificationChannels: []  # Add notification channel IDs here
  
  severity: ERROR
  
  enabled: true

---

# Alert Policy 2: Circuit Breaker State OPEN
circuit_breaker_open:
  displayName: "MCP Rules Gateway - Circuit Breaker Open"
  documentation:
    content: |
      This alert fires when the circuit breaker state changes to OPEN, indicating
      that the MCP Rules Engine backend is experiencing failures.
      
      ## What this means:
      - The circuit breaker has detected repeated failures
      - Traffic to the backend is being blocked to prevent cascading failures
      - Users will receive fallback responses or errors
      
      ## Immediate Actions:
      1. Check Cloud Run service health and logs
      2. Verify database connectivity
      3. Check for resource exhaustion (CPU, memory)
      4. Review recent deployments
      
      ## Recovery:
      - Circuit breaker will automatically attempt to close after the timeout period
      - Manual intervention may be required to fix underlying issues
      - Monitor error rates and latency after recovery
      
      ## Escalation:
      - Immediate: Notify on-call engineer
      - If not resolved in 15 minutes: Escalate to senior engineer
    mimeType: "text/markdown"
  
  combiner: OR
  
  conditions:
    - displayName: "Circuit breaker state is OPEN"
      conditionMatchedLog:
        filter: |
          resource.type="cloud_run_revision"
          AND (
            jsonPayload.breaker_state="OPEN"
            OR jsonPayload.circuit_breaker_state="OPEN"
            OR jsonPayload.message=~"circuit.*breaker.*open"
            OR jsonPayload.message=~"breaker.*open"
          )
        labelExtractors:
          service_name: "EXTRACT(resource.labels.service_name)"
          revision_name: "EXTRACT(resource.labels.revision_name)"
  
  alertStrategy:
    autoClose: "900s"  # 15 minutes
    
  notificationChannels: []  # Add notification channel IDs here
  
  severity: CRITICAL
  
  enabled: true

---

# Alert Policy 3: High Latency (P95 > 700ms)
api_gateway_high_latency:
  displayName: "MCP Rules Gateway - High Latency"
  documentation:
    content: |
      This alert fires when the 95th percentile latency exceeds 700ms for 5 minutes.
      
      ## Impact:
      - User experience degradation
      - Potential timeout issues
      - Reduced system throughput
      
      ## Investigation Steps:
      1. Check Cloud Run instance scaling and cold starts
      2. Review database query performance
      3. Check network connectivity issues
      4. Verify API Gateway configuration
      5. Monitor resource utilization (CPU, memory)
      
      ## Mitigation:
      - Scale up Cloud Run instances if needed
      - Optimize slow database queries
      - Consider caching strategies
      - Review and optimize code paths
    mimeType: "text/markdown"
  
  combiner: OR
  
  conditions:
    - displayName: "P95 latency > 700ms for 5 minutes"
      conditionThreshold:
        filter: |
          metric.type="serviceruntime.googleapis.com/api/request_count"
          AND resource.labels.service="mcp-rules-gateway"
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 700.0
        duration: "300s"
        
        aggregations:
          - alignmentPeriod: "60s"
            perSeriesAligner: ALIGN_DELTA
            crossSeriesReducer: REDUCE_PERCENTILE_95
            groupByFields:
              - "resource.label.service"
        
        trigger:
          count: 1
  
  alertStrategy:
    autoClose: "1800s"  # 30 minutes
    
  notificationChannels: []  # Add notification channel IDs here
  
  severity: WARNING
  
  enabled: true

---

# Alert Policy 4: API Key Quota Exhaustion
api_key_quota_exhaustion:
  displayName: "MCP Rules Gateway - API Key Quota Exhaustion"
  documentation:
    content: |
      This alert fires when API key quotas are being exhausted, indicating
      potential abuse or unexpected traffic spikes.
      
      ## Symptoms:
      - Increased 429 (Too Many Requests) responses
      - Legitimate users being rate limited
      - Potential service degradation
      
      ## Investigation:
      1. Identify which API keys are hitting limits
      2. Check for unusual traffic patterns
      3. Verify rate limiting configuration
      4. Look for potential abuse or bot traffic
      
      ## Actions:
      - Temporarily increase quotas if legitimate traffic
      - Block or throttle abusive API keys
      - Implement additional rate limiting if needed
      - Contact affected tenants if necessary
    mimeType: "text/markdown"
  
  combiner: OR
  
  conditions:
    - displayName: "High rate of 429 responses"
      conditionThreshold:
        filter: |
          resource.type="api"
          AND resource.labels.service="mcp-rules-gateway"
          AND metric.labels.response_code="429"
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 10.0
        duration: "300s"
        
        aggregations:
          - alignmentPeriod: "60s"
            perSeriesAligner: ALIGN_RATE
            crossSeriesReducer: REDUCE_SUM
            groupByFields:
              - "resource.label.service"
              - "metric.label.api_key"
        
        trigger:
          count: 1
  
  alertStrategy:
    autoClose: "1800s"  # 30 minutes
    
  notificationChannels: []  # Add notification channel IDs here
  
  severity: WARNING
  
  enabled: true
