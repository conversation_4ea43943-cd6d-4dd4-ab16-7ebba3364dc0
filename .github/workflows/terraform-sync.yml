name: Terraform State Sync

on:
  schedule:
    # Run nightly at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:
    inputs:
      force_apply:
        description: 'Force apply even if unexpected changes detected'
        required: false
        default: 'false'
        type: boolean
      skip_cleanup:
        description: 'Skip orphan cleanup (for after first successful run)'
        required: false
        default: 'false'
        type: boolean

jobs:
  cleanup_orphans:
    runs-on: ubuntu-latest
    environment: production
    # Only run cleanup if not explicitly skipped
    if: github.event.inputs.skip_cleanup != 'true'
    outputs:
      cleanup_completed: ${{ steps.cleanup.outputs.cleanup_completed }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: texas-laws-personalinjury

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"
          terraform_wrapper: false

      - name: Terraform Init
        working-directory: infra/terraform
        run: |
          terraform init -upgrade
          echo "✅ Terraform initialized for cleanup"

      - name: Run Orphan Cleanup
        id: cleanup
        env:
          CLOUD_RUN_URL: ${{ secrets.CLOUD_RUN_URL }}
        run: |
          echo "🧹 Running orphan resource cleanup..."
          ./scripts/cleanup-orphans.sh
          echo "cleanup_completed=true" >> $GITHUB_OUTPUT
          echo "✅ Orphan cleanup completed successfully"

  state_sync:
    runs-on: ubuntu-latest
    environment: production
    needs: cleanup_orphans
    # Run if cleanup was skipped OR if cleanup completed successfully
    if: always() && (github.event.inputs.skip_cleanup == 'true' || needs.cleanup_orphans.result == 'success')
    outputs:
      gateway_host: ${{ steps.terraform_apply.outputs.gateway_host }}
      changes_detected: ${{ steps.terraform_plan.outputs.changes_detected }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: texas-laws-personalinjury

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"
          terraform_wrapper: false

      - name: Terraform Init
        working-directory: infra/terraform
        run: |
          terraform init -upgrade
          echo "✅ Terraform initialized and upgraded"

      - name: Remove function_source from state
        working-directory: infra/terraform
        run: |
          set +e
          terraform state list | grep function_source | xargs -r terraform state rm
          echo "✅ Removed function_source from state if present"
          true

      - name: Terraform Plan
        id: terraform_plan
        working-directory: infra/terraform
        run: |
          set +e  # Don't exit on non-zero exit codes
          
          terraform plan \
            -var="cloud_run_url=${{ secrets.CLOUD_RUN_URL }}" \
            -detailed-exitcode \
            -out=tfplan
          
          PLAN_EXIT_CODE=$?
          echo "plan_exit_code=$PLAN_EXIT_CODE" >> $GITHUB_OUTPUT
          
          if [ $PLAN_EXIT_CODE -eq 0 ]; then
            echo "changes_detected=false" >> $GITHUB_OUTPUT
            echo "✅ No changes detected - infrastructure is in sync"
          elif [ $PLAN_EXIT_CODE -eq 0 ]; then
            # Already handled above, but keep for clarity
            :
          elif [ $PLAN_EXIT_CODE -eq 2 ]; then
            echo "changes_detected=true" >> $GITHUB_OUTPUT
            echo "⚠️ Changes detected - analyzing plan..."
            
            # Show the plan for review
            terraform show tfplan
            
            # Check if changes are only expected resource types (acceptable drift)
            terraform show -json tfplan > plan.json

            # Extract resource changes and check if they're expected types
            # Expected types: monitoring alerts, API config, SSL cert, gateway metadata
            UNEXPECTED_CHANGES=$(jq -r '
              .resource_changes[]? |
              select(.change.actions[] | contains("create") or contains("update") or contains("delete")) |
              select(.type | test("^google_(monitoring_alert_policy|api_gateway_api_config|compute_managed_ssl_certificate)$") | not) |
              select(.address != "google_api_gateway_gateway.prod") |
              select(.type | test("^google_(project_iam_|secret_manager_secret)") | not) |
              .address
            ' plan.json)

            if [ -n "$UNEXPECTED_CHANGES" ]; then
              echo "❌ Unexpected changes detected in non-expected resources:"
              echo "$UNEXPECTED_CHANGES"

              if [ "${{ github.event.inputs.force_apply }}" != "true" ]; then
                echo "::error::Unexpected infrastructure changes detected. Use force_apply=true to override."
                exit 1
              else
                echo "⚠️ Force apply enabled - proceeding despite unexpected changes"
              fi
            else
              echo "✅ Only expected resource types detected (monitoring, API config, SSL cert, gateway metadata)"
            fi
          else
            echo "❌ Terraform plan failed with exit code $PLAN_EXIT_CODE"
            exit $PLAN_EXIT_CODE
          fi

      - name: Terraform Apply
        id: terraform_apply
        if: steps.terraform_plan.outputs.changes_detected == 'true'
        working-directory: infra/terraform
        run: |
          echo "🚀 Applying Terraform changes..."
          terraform apply -auto-approve tfplan
          
          # Extract gateway host for downstream jobs
          GATEWAY_HOST=$(terraform output -raw gateway_host 2>/dev/null || echo "")
          echo "gateway_host=$GATEWAY_HOST" >> $GITHUB_OUTPUT
          
          echo "✅ Terraform apply completed successfully"

      - name: Upload Plan Artifact
        if: steps.terraform_plan.outputs.changes_detected == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: terraform-plan-${{ github.run_id }}
          path: |
            infra/terraform/tfplan
            infra/terraform/plan.json
          retention-days: 30

  apply_alerts:
    runs-on: ubuntu-latest
    needs: state_sync
    if: always() && (needs.state_sync.result == 'success')
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: texas-laws-personalinjury

      - name: Apply Alert Policies
        run: |
          echo "🔔 Applying monitoring alert policies..."
          ./scripts/apply-alert-policies.sh
          echo "✅ Alert policies applied successfully"

  verify_gateway:
    runs-on: ubuntu-latest
    needs: [state_sync, apply_alerts]
    if: always() && (needs.state_sync.result == 'success' && needs.apply_alerts.result == 'success')
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: texas-laws-personalinjury

      - name: Verify API Gateway
        run: |
          echo "🔍 Verifying API Gateway health..."
          ./scripts/verify-api-gateway-prod.sh
          echo "✅ API Gateway verification completed successfully"

      - name: Verify Tenant API Key Provisioning
        run: |
          echo "🔑 Verifying tenant API key provisioning..."
          ./scripts/verify-tenant-api-key.sh 89be252b-021c-45ab-b55f-41b3a913c760
          echo "✅ Tenant API key verification completed successfully"

      - name: Summary
        run: |
          echo "## Terraform Sync Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Cleanup Orphans**: ${{ needs.cleanup_orphans.result || 'Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **State Sync**: ${{ needs.state_sync.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Changes Detected**: ${{ needs.state_sync.outputs.changes_detected }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Alert Policies**: ${{ needs.apply_alerts.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Gateway Verification**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **API Key Provisioning**: ✅ Verified" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.state_sync.outputs.gateway_host }}" != "" ]; then
            echo "- **Gateway Host**: ${{ needs.state_sync.outputs.gateway_host }}" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "✅ All sync operations completed successfully" >> $GITHUB_STEP_SUMMARY

          # Add note about disabling cleanup after first success
          if [ "${{ needs.cleanup_orphans.outputs.cleanup_completed }}" == "true" ]; then
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "🔧 **Next Steps**: Consider setting \`skip_cleanup=true\` for future runs since orphan cleanup completed successfully." >> $GITHUB_STEP_SUMMARY
          fi
